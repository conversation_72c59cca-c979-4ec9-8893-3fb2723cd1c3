const User = require('../entity/user')
const UserMapper = require('../mapper/user')
const Face = require('../../../hardware/face/index')
const Finger = require('../../../hardware/sign_finger/libs/index')
const {
	generateToken
} = require('../jwt/index')
const {
	generateTime
} = require('../tools/index')
const {
	CODE_ENUM,
	CODE_DIC
} = require('../enum')
const {
	aesEncrypt
} = require('../crypto/index')
const RESET_PASSWORD = '123456'
const commonMapper = require('../mapper/business')
const XLSX = require('xlsx')
const querystring = require('querystring')
const {
	SYSTEM_PLATFORM_STR_DIC
} = require('../../../hardware/enum')
const {
	createDir,
	deleteFolderRecursively
} = require('../tools/index')
const Logger = require('../../../hardware/log/index')
const log = new Logger('file-service')
const path = require('path')
const fs = require('fs')
const AdmZip = require('adm-zip')
const http = require('../../../hardware/lib/request')
const {
	v4: uuidv4
} = require('uuid')
async function readBinaryFileToBuffer(filePath) {
	return new Promise((resolve, reject) => {
		fs.readFile(filePath, (err, data) => {
			if (err) {
				reject(err)
			} else {
				resolve(data)
			}
		})
	})
}
const UserService = {
	login: (userInfo) => {
		return new Promise(async (resolve) => {
			let user = new User(userInfo)
			if (!user.loginId) {
				return resolve({
					code: CODE_ENUM.UNAUTHORIZED,
					msg: '用户名不能为空！'
				})
			}
			if (!user.password) {
				return resolve({
					code: CODE_ENUM.UNAUTHORIZED,
					msg: '密码不能为空！'
				})
			}
			// 信息比对
			const info = await UserMapper.select(user)
			if (!info) {
				return resolve({
					code: CODE_ENUM.UNAUTHORIZED,
					msg: '登录失败，用户名不存在！'
				})
			}
			if (info.password != user.password) {
				return resolve({
					code: CODE_ENUM.UNAUTHORIZED,
					msg: '登录失败，密码错误！'
				})
			}

			user = {
				...user,
				...info
			}
			delete user.password
			user.loginTime = generateTime(new Date(), 'datetime')
			await UserMapper.updateLoginTime(user)
			// 根据查询结果生成token
			user.token = generateToken()
			resolve({
				code: CODE_ENUM.OK,
				msg: '登录成功',
				data: user
			})
		})
	},
	findInfoById: (id) => {
		return new Promise(async (resolve) => {
			if (!id) {
				return resolve({
					code: CODE_ENUM.SERVER_ERROR,
					msg: '用户ID不能为空！'
				})
			}
			const userInfo = await UserMapper.findInfoById(id)
			if (!userInfo) {
				return resolve({
					code: CODE_ENUM.PARAMS_ERROR,
					msg: '用户名不存在！'
				})
			}
			resolve({
				code: CODE_ENUM.OK,
				msg: '查询成功',
				data: userInfo
			})
		})
	},
	register: (userInfo) => {
		log.info(userInfo, 'userInfouserInfouserInfo')
		return new Promise(async (resolve) => {
			const user = new User(userInfo)
			user.age = userInfo.age
			user.sex = userInfo.sex
			if (user.loginId) {
				if (!user.password) {
					return resolve({
						code: CODE_ENUM.PARAMS_ERROR,
						msg: '密码不能为空！'
					})
				}
				const userRows = await UserMapper.select(user)
				if (userRows) {
					return resolve({
						code: CODE_ENUM.SERVER_ERROR,
						msg: '用户名已被注册！'
					})
				}
			}
			// 查询是否存在人脸
			if (user.faceImg) {
				let faceApi = null
				try {
					const userfaceRows = await UserService.faceCompareManyByBase64({
						type: Face.GXX_FACE_1,
						base64: user.faceImg,
						thresold: 0.82
					})
					if (userfaceRows.data && userfaceRows.data.length) {
						return resolve({
							code: CODE_ENUM.SERVER_ERROR,
							msg: '用户人脸已被注册！'
						})
					}
					faceApi = new Face(Face.GXX_FACE_1)
					user.feature = faceApi.getFaceFeature(user.faceImg)
					faceApi = null
				} catch (error) {
					return resolve({
						code: CODE_ENUM.SERVER_ERROR,
						msg: '人脸识别异常！'
					})
				}
			}
			// 查询是否存在指纹
			if (user.fingerprint) {
				try {
					const userFingerprintRows = await UserService.fingerCompareManyByFingerFeature({
						fingerFeature: user.fingerprint
					})
					if (userFingerprintRows.data && userFingerprintRows.data.length) {
						return resolve({
							code: CODE_ENUM.SERVER_ERROR,
							msg: '用户指纹已被注册！'
						})
					}
				} catch (error) {
					return resolve({
						code: CODE_ENUM.SERVER_ERROR,
						msg: '指纹识别异常！'
					})
				}
			}
			// 查询是否相同警号
			if (user.policeNumber) {
				const userRows = await UserMapper.selectPoliceNumber(user.policeNumber)
				if (userRows) {
					return resolve({
						code: CODE_ENUM.SERVER_ERROR,
						msg: '警号已被注册！'
					})
				}
			}
			const time = generateTime(new Date(), 'datetime')
			user.createTime = time
			user.updateTime = time
			const rows = await UserMapper.register(user)
			if (!rows) {
				return resolve({
					code: CODE_ENUM.SERVER_ERROR,
					msg: '注册异常！'
				})
			}
			//start 插入用户之后,需要把关联的柜子,增加关联关系
			if (user.associatedIscds) {
				const arr = user.associatedIscds.split(',')
				arr.forEach((name) => {
					const params = {
						sidesName: name,
						userType: user.userType,
						userId: rows,
						userName: user.name,
						isDeleted: 'N'
					}
					commonMapper.addIscdsUserList(params)
				})
			}
			//end
			resolve({
				code: CODE_ENUM.OK,
				msg: '注册成功'
			})
		})
	},
	resetPassword: (id) => {
		return new Promise(async (resolve) => {
			const user = new User({
				id
			})
			user.password = aesEncrypt(RESET_PASSWORD)
			user.updateTime = generateTime(new Date(), 'datetime')
			const rows = await UserMapper.resetPassword(user)
			if (!rows) {
				return resolve({
					code: CODE_ENUM.SERVER_ERROR,
					msg: '重置失败！',
					data: ''
				})
			}
			resolve({
				code: CODE_ENUM.OK,
				msg: '重置成功',
				data: RESET_PASSWORD
			})
		})
	},
	updatePassword(body) {
		return new Promise(async (resolve, reject) => {
			const {
				id,
				oldPassword,
				newPassword,
				confirmNewPassword
			} = body
			if (!id || !oldPassword || !newPassword || !confirmNewPassword) {
				return resolve({
					code: CODE_ENUM.PARAMS_ERROR,
					msg: CODE_DIC[CODE_ENUM.PARAMS_ERROR]
				})
			}
			const user = await UserMapper.findInfoById(id)
			if (!user) {
				return resolve({
					code: CODE_ENUM.PARAMS_ERROR,
					msg: '用户不存在！'
				})
			}
			if (newPassword !== confirmNewPassword) {
				return resolve({
					code: CODE_ENUM.PARAMS_ERROR,
					msg: '两次密码不一致！'
				})
			}
			if (user.password !== oldPassword) {
				return resolve({
					code: CODE_ENUM.PARAMS_ERROR,
					msg: '原始密码不正确！'
				})
			}
			const bool = await UserMapper.updatePassword({
				...user,
				password: newPassword,
				updateTime: generateTime(new Date(), 'datetime')
			})
			if (!bool) {
				return resolve({
					code: CODE_ENUM.SERVER_ERROR,
					msg: '密码修改失败！'
				})
			}
			resolve({
				code: CODE_ENUM.OK,
				msg: '密码修改成功'
			})
		})
	},
	update: (userInfo) => {
		return new Promise(async (resolve) => {
			const user = new User(userInfo)
			if (!user.id) {
				return resolve({
					code: CODE_ENUM.PARAMS_ERROR,
					msg: '用户不存在！'
				})
			}
			if (user.userType != 3 && !user.loginId) {
				return resolve({
					code: CODE_ENUM.PARAMS_ERROR,
					msg: '用户名不能为空！'
				})
			}
			//检查是否不一样id并且人脸一致的数据
			if (user.faceImg) {
				let faceApi = null
				try {
					const userfaceRows = await UserService.faceCompareManyByBase64({
						type: Face.GXX_FACE_1,
						base64: user.faceImg
					})
					if (userfaceRows.data && userfaceRows.data.length && userfaceRows.data[0].id != user.id) {
						return resolve({
							code: CODE_ENUM.SERVER_ERROR,
							msg: '用户人脸已被注册！'
						})
					}
					faceApi = new Face(Face.GXX_FACE_1)
					user.feature = faceApi.getFaceFeature(user.faceImg)
					faceApi = null
				} catch (error) {
					faceApi = null
					return resolve({
						code: CODE_ENUM.SERVER_ERROR,
						msg: '人脸识别异常！'
					})
				}
			}
			//检查是否不一样id并且指纹一致的数据
			if (user.fingerprint) {
				try {
					const userFingerprintRows = await UserService.fingerCompareManyByFingerFeature({
						fingerFeature: user.fingerprint
					})
					if (userFingerprintRows.data && userFingerprintRows.data.length && userFingerprintRows.data[0].id != user.id) {
						return resolve({
							code: CODE_ENUM.SERVER_ERROR,
							msg: '用户指纹已被注册！'
						})
					}
				} catch (error) {
					return resolve({
						code: CODE_ENUM.SERVER_ERROR,
						msg: '指纹识别异常！'
					})
				}
			}
			user.updateTime = generateTime(new Date(), 'datetime')
			const rows = await UserMapper.update(user)
			if (!rows) {
				return resolve({
					code: CODE_ENUM.SERVER_ERROR,
					msg: '更新异常！'
				})
			}
			//start 民警使用的时候，修改用户之后,需要判断如果存在关联,则添加新的关联，如果没有，则需要查询是否已有的，进行取关。
			if (user.userType == 2) {
				if (user.associatedIscds) {
					const params = {
						sidesNames: user.associatedIscds,
						faceImg: user.faceImg,
						userType: user.userType,
						userId: user.id,
						userName: user.name
					}
					commonMapper.updateIscdsList(params)
				} else {
					commonMapper.batchDelete('sides_user', 'userId', user.id)
				}
			}
			//end
			resolve({
				code: CODE_ENUM.OK,
				msg: '更新成功'
			})
		})
	},
	delete: (id) => {
		return new Promise(async (resolve) => {
			if (!id) {
				return resolve({
					code: CODE_ENUM.PARAMS_ERROR,
					msg: '用户不存在！'
				})
			}
			const rows = await UserMapper.delete(id)
			if (!rows) {
				return resolve({
					code: CODE_ENUM.SERVER_ERROR,
					msg: '删除异常！'
				})
			}
			resolve({
				code: CODE_ENUM.OK,
				msg: '删除成功'
			})
		})
	},
	//批量删除
	batchDelete: (ids) => {
		return new Promise(async (resolve) => {
			const rows = await commonMapper.batchDelete('user', 'id', ids)
			if (!rows) {
				return resolve({
					code: CODE_ENUM.SERVER_ERROR,
					msg: '删除异常！'
				})
			}
			// 删除民警所关联的嫌疑人
			commonMapper.batchDelete('user', 'associatedPoliceId', ids)
			//删除完人员表，需要接着删除柜格关联人员表
			commonMapper.batchDelete('sides_user', 'userId', ids)
			resolve({
				code: CODE_ENUM.OK,
				msg: '删除成功'
			})
		})
	},
	pageList: (body) => {
		return new Promise(async (resolve) => {
			const {
				page,
				size
			} = body
			if (!page || !size) {
				return resolve({
					code: CODE_ENUM.PARAMS_ERROR,
					msg: '参数异常！'
				})
			}
			const user = new User(body)
			const {
				data,
				total,
				totalPages,
				success
			} = await UserMapper.pageList(page, size, user)
			if (!success) {
				return resolve({
					code: CODE_ENUM.SERVER_ERROR,
					msg: '查询异常！',
					data: {
						data,
						total,
						totalPages,
						page,
						size
					}
				})
			}
			resolve({
				code: CODE_ENUM.OK,
				msg: '查询成功',
				data: {
					data,
					total,
					totalPages,
					page,
					size
				}
			})
		})
	},
	/**
	 * 获取人脸检测信息
	 * @param {Object} body
	 * @param {Number} body.type 算法模式 1: GXX-FACE-1
	 * @param {Object} body.base64 base64 图片
	 * @returns {Object}
	 */
	getFaceDetectInfo: (body) => {
		if (!body.base64) {
			return {
				code: CODE_ENUM.PARAMS_ERROR,
				msg: CODE_DIC[CODE_ENUM.PARAMS_ERROR]
			}
		}
		let faceApi = null
		try {
			faceApi = new Face(body.type)
			const rect = faceApi.getFaceDetectInfo(body.base64)
			faceApi = null
			if (!rect) {
				return {
					code: CODE_ENUM.OK,
					msg: '未检测到人脸'
				}
			}
			return {
				code: CODE_ENUM.OK,
				msg: '检测成功',
				data: rect
			}
		} catch (error) {
			faceApi = null
			return {
				code: CODE_ENUM.SERVER_ERROR,
				msg: CODE_DIC[CODE_ENUM.SERVER_ERROR]
			}
		}
	},
	/**
	 * 人脸比对 1:N
	 * @param {Object} body
	 * @param {Number} body.type 算法模式 1: GXX-FACE-1
	 * @param {Object} body.base64 base64 图片
	 * @param {Number} body.thresold
	 * @param {Number} body.maxNum
	 * @returns {Object}
	 */
	faceCompareManyByBase64: (body) => {
		return new Promise(async (resolve) => {
			if (!body.base64) {
				return resolve({
					code: CODE_ENUM.PARAMS_ERROR,
					msg: CODE_DIC[CODE_ENUM.PARAMS_ERROR]
				})
			}
			// 获取所有人员信息
			const personList = await UserMapper.getPersonList()
			if (!personList.length) {
				return resolve({
					code: CODE_ENUM.OK,
					msg: '人员未注册！'
				})
			}
			let faceApi = null
			try {
				faceApi = new Face(body.type)
				const rect = faceApi.faceCompareManyByBase64(body, personList)
				faceApi = null
				if (!rect) {
					return resolve({
						code: CODE_ENUM.OK,
						msg: '人脸比对失败'
					})
				}
				// 根据人员id 获取人员信息
				const promiseAll = []
				const len = rect.length
				for (let i = 0; i < len; i++) {
					const item = rect[i]
					if (item.id) {
						promiseAll.push(UserMapper.findInfoById(item.id))
					}
				}
				let list = await Promise.all(promiseAll)
				list = list.flat()
				list.forEach((person, i) => {
					person.token = generateToken()
					Object.assign(person, rect[i])
					delete person.password
					delete person.feature
					delete person.isDeleted
				})
				resolve({
					code: CODE_ENUM.OK,
					msg: list.length ? '人脸比对成功' : '人员未注册！',
					data: list
				})
			} catch (error) {
				faceApi = null
				resolve({
					code: CODE_ENUM.SERVER_ERROR,
					msg: CODE_DIC[CODE_ENUM.SERVER_ERROR]
				})
			}
		})
	},
	gestureLogin: (userInfo) => {
		return new Promise(async (resolve) => {
			let user = new User(userInfo)
			if (!user.gesture) {
				return resolve({
					code: CODE_ENUM.UNAUTHORIZED,
					msg: '手势不能为空！'
				})
			}
			// 信息比对
			const rows = await UserMapper.gestureSelect(user)
			if (rows.length == 0) {
				return resolve({
					code: CODE_ENUM.UNAUTHORIZED,
					msg: '用户不存在！'
				})
			}
			user = {
				...user,
				...rows[0]
			}
			delete user.password
			user.loginTime = generateTime(new Date(), 'datetime')
			await UserMapper.updateLoginTime(user)
			// 根据查询结果生成token
			user.token = generateToken()
			resolve({
				code: CODE_ENUM.OK,
				msg: '登录成功',
				data: user
			})
		})
	},
	/**
	 * 指纹特征值比对
	 */
	fingerCompareManyByFingerFeature: (body) => {
		return new Promise(async (resolve) => {
			if (!body.fingerFeature) {
				resolve({
					code: CODE_ENUM.PARAMS_ERROR,
					msg: CODE_DIC[CODE_ENUM.PARAMS_ERROR]
				})
				return
			}
			// 获取所有人员信息
			const personList = await UserMapper.getFeaturePersonList()
			if (!personList.length) {
				resolve({
					code: CODE_ENUM.OK,
					msg: '人员未注册！'
				})
			}
			let fingerApi = null
			try {
				fingerApi = new Finger()
				const bool = fingerApi.init() && fingerApi.openDevices()
				if (!bool) {
					resolve({
						code: CODE_ENUM.SERVER_ERROR,
						msg: CODE_DIC[CODE_ENUM.SERVER_ERROR]
					})
					return
				}
				let info = null
				for (let i = 0; i < personList.length; i++) {
					const rect = fingerApi.fingerFeatureComparison(body.fingerFeature, personList[i].fingerprint)
					const fingerThreshold = global.serverConfig.businessInfo.fingerThreshold || 50
					if (rect > fingerThreshold) {
						info = personList[i]
						info.featureValue = rect
						break
					}
				}
				if (!info) {
					resolve({
						code: CODE_ENUM.OK,
						msg: '指纹比对失败'
					})
					return
				}
				// 根据人员id 获取人员信息
				let infoDetail = null
				if (info.id) {
					infoDetail = await UserMapper.findInfoById(info.id)
					infoDetail.token = generateToken()
					infoDetail.featureValue = info.featureValue
					delete infoDetail.password
					delete infoDetail.feature
					delete infoDetail.isDeleted
				}
				resolve({
					code: CODE_ENUM.OK,
					msg: infoDetail ? '指纹比对成功' : '人员未注册！',
					data: [infoDetail]
				})
			} catch (error) {
				fingerApi = null
				resolve({
					code: CODE_ENUM.SERVER_ERROR,
					msg: CODE_DIC[CODE_ENUM.SERVER_ERROR]
				})
			}
		})
	},
	//用户导入
	userListImport: (boundary, body) => {
		return new Promise(async (resolve) => {
			let fileName = '' // 文件名
			const file = querystring.parse(body, '\r\n', ':')
			const fileInfo = file['Content-Disposition'].split('; ')
			for (const value in fileInfo) {
				const item = fileInfo[value]
				if (item.indexOf('filename=') != -1) {
					fileName = item.substring(10, item.length - 1)
					const index = fileName.lastIndexOf(process.platform == SYSTEM_PLATFORM_STR_DIC.LINUX ? '/' : '\\')
					fileName = fileName.substring(index + 1)
				}
			}
			const entireData = body.toString()
			const contentType = file['Content-Type'].substring(1)
			const upperBoundary = entireData.indexOf(contentType) + contentType.length
			const shorterData = entireData.substring(upperBoundary)
			const binaryDataAlmost = shorterData.replace(/^\s\s*/, '').replace(/\s\s*$/, '')
			const binaryData = binaryDataAlmost.substring(0, binaryDataAlmost.indexOf(`--${boundary}--`))
			const workbook = XLSX.read(binaryData, {
				type: 'binary'
			})
			const sheetName = workbook.SheetNames[0]
			const worksheet = workbook.Sheets[sheetName]
			const data = XLSX.utils.sheet_to_json(worksheet)
			const resList = []
			for (const item of data) {
				if (!item.账号) {
					return resolve({
						code: CODE_ENUM.PARAMS_ERROR,
						msg: '存在账号为空数据,请重新上传'
					})
				}
				const user = {
					loginId: item.账号,
					name: item.名称,
					policeNumber: item.警号,
					userType: '2',
					password: aesEncrypt(RESET_PASSWORD),
					sex: 1
				}
				const res = await UserService.register(user)
				res.user = item.账号
				resList.push(res)
			}
			resolve({
				code: CODE_ENUM.OK,
				msg: '用户导入成功',
				data: resList
			})
		})
	},
	//用户照片导入
	faceListImport: (boundary, body) => {
		return new Promise(async (resolve) => {
			createDir(global.packagePath)
			let fileName = '' // 文件名
			// 边界字符串
			const file = querystring.parse(body, '\r\n', ':')
			// 获取文件名
			const fileInfo = file['Content-Disposition'].split('; ')
			for (const value in fileInfo) {
				const item = fileInfo[value]
				if (item.indexOf('filename=') != -1) {
					fileName = item.substring(10, item.length - 1)
					const index = fileName.lastIndexOf(process.platform == SYSTEM_PLATFORM_STR_DIC.LINUX ? '/' : '\\')
					fileName = fileName.substring(index + 1)
				}
			}
			const entireData = body.toString()
			const contentType = file['Content-Type'].substring(1)
			const upperBoundary = entireData.indexOf(contentType) + contentType.length
			const shorterData = entireData.substring(upperBoundary)
			const binaryDataAlmost = shorterData.replace(/^\s\s*/, '').replace(/\s\s*$/, '')
			const binaryData = binaryDataAlmost.substring(0, binaryDataAlmost.indexOf(`--${boundary}--`))
			fs.writeFile(`${global.packagePath}/faceBox`, binaryData, 'binary', (err) => {
				if (err) {
					log.info(`文件上传失败, err: ${err}`)
					resolve({
						code: CODE_ENUM.SERVER_ERROR,
						msg: `文件上传失败, err: ${err}`
					})
				}
			})
			const buffer = await readBinaryFileToBuffer(`${global.packagePath}/faceBox`)
			const zipBinaryData = Buffer.from(buffer)
			const zip = new AdmZip(zipBinaryData)
			// 解压所有文件到指定目录
			zip.extractAllTo(`${global.packagePath}/faceListBox`, true)
			fs.readdir(`${global.packagePath}/faceListBox`, (err, files) => {
				if (err) {
					resolve({
						code: CODE_ENUM.SERVER_ERROR,
						msg: `读取文件夹时出错, err: ${err}`
					})
					return
				}
				const promises = files.map((file) => {
					return new Promise((resolve, reject) => {
						const filePath = path.join(`${global.packagePath}/faceListBox`, file)
						fs.readFile(filePath, async (err, data) => {
							if (err) {
								console.log(err)
							} else {
								const faceImg = `data:image/jpeg;base64,${data.toString('base64')}`
								const lastDotIndex = file.lastIndexOf('.')
								const loginId = file.substring(0, lastDotIndex)
								resolve({
									loginId,
									faceImg
								})
							}
						})
					})
				})
				Promise.all(promises)
					.then(async (results) => {
						const resList = []
						for (let i = 0; i < results.length; i++) {
							const faceApi = new Face(Face.GXX_FACE_1)
							const feature = faceApi.getFaceFeature(results[i].faceImg)
							if (feature) {
								const updateResult = await UserMapper.updateFaceImg({
									loginId: results[i].loginId,
									faceImg: results[i].faceImg,
									feature
								})
								if (updateResult) {
									resList.push({
										user: results[i].loginId,
										code: CODE_ENUM.OK,
										msg: '替换成功'
									})
								} else {
									resList.push({
										user: results[i].loginId,
										code: CODE_ENUM.PARAMS_ERROR,
										msg: '替换失败'
									})
								}
							} else {
								resList.push({
									user: results[i].loginId,
									code: CODE_ENUM.PARAMS_ERROR,
									msg: '人脸特征值获取失败'
								})
							}
						}
						deleteFolderRecursively(`${global.packagePath}/faceListBox`)
						resolve({
							code: CODE_ENUM.OK,
							msg: '用户照片导入成功',
							data: resList
						})
					})
					.catch((err) => {
						deleteFolderRecursively(`${global.packagePath}/faceListBox`)
					})
			})
		})
	},
	allUserList: () => {
		return new Promise(async (resolve) => {
			const userData = await commonMapper.findExistAllList('user', true)
			resolve({
				code: CODE_ENUM.OK,
				msg: '查询成功',
				data: userData.data
			})
		})
	},
	synchronizePersonnel_old(body) {
		const url = `http://${body.ip}:${body.port}/api/user/allUserList`
		return new Promise(async (resolve) => {
			http({
				method: 'get',
				url,
				headers: {
					'auth-key': 'app'
				}
			}).then(
				async (res) => {
						const {
							data
						} = res
						if (!data.length) {
							return resolve({
								code: CODE_ENUM.PARAMS_ERROR,
								msg: '目标主机无人员数据'
							})
						}
						resolve({
							code: CODE_ENUM.OK,
							msg: '用户同步中，请稍后尝试'
						})
						for (let i = 0; i < data.length; i++) {
							const item = data[i]
							if (item.associatedPoliceNumber) {
								const userRows = await UserMapper.selectPoliceNumber(item.associatedPoliceNumber)
								item.associatedPoliceId = userRows.id
								item.associatedPoliceName = userRows.name
							}
							item.id = uuidv4()
							await UserService.register(item)
						}
					},
					(err) => {
						resolve({
							code: CODE_ENUM.PARAMS_ERROR,
							msg: '操作失败'
						})
					}
			).catch(
				(err) => {

				}
			)
		})
	},
	synchronizePersonnel(body) {
		console.log(body, 'synchronizePersonnel开始调用')
		log.info(body, 'bodybodybody')
		const url = `/ptm-com/acp/pm/cnpFace/wpg/list`
		return new Promise(async (resolve) => {
			http({
				method: 'get',
				url,
				headers: {
					'auth-key': 'app'
				}
			}).then(
				async (res) => {
						const {
							data
						} = res
						log.info('/acp/pm/cnpFace/wpg/list:', res, 'bodybodybody')
						if (!data.length) {
							return resolve({
								code: CODE_ENUM.PARAMS_ERROR,
								msg: '目标主机无人员数据'
							})
						}
						resolve({
							code: CODE_ENUM.OK,
							msg: '用户同步中，请稍后尝试'
						})
						for (let i = 0; i < data.length; i++) {
							const item = data[i]
							if (item.associatedPoliceNumber) {
								const userRows = await UserMapper.selectPoliceNumber(item.associatedPoliceNumber)
								item.associatedPoliceId = userRows.id
								item.associatedPoliceName = userRows.name
							}
							item.id = uuidv4()
							await UserService.register(item)
						}
					},
					(err) => {
						log.info(err,CODE_ENUM.PARAMS_ERROR, 'synchronizePersonnel调用失败')
						resolve({
							code: CODE_ENUM.PARAMS_ERROR,
							msg: '操作失败'
						})
					}
			).catch(
				(err) => {
					log.info(err, 'synchronizePersonnel调用失败')
				}
			)
		})
	}
}

module.exports = UserService
